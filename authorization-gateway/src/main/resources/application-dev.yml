spring:
  cloud:
    gateway:
      # 全局CORS配置
      server:
        webflux:
          routes:
            # 认证服务路由
            - id: auth-server
              uri: http://127.0.0.1:8889
              predicates:
                - Path=/auth-server/**
              filters:
                - StripPrefix=1

            # 业务服务路由示例（可根据实际需要配置）
            - id: luc-system
              uri: http://127.0.0.1:19003
              predicates:
                - Path=/luc-system/**
              filters:
                - StripPrefix=1
                # 添加用户信息到请求头
                - AddRequestHeader=X-App, luc-system

            # 业务服务路由示例（可根据实际需要配置）
            - id: product-center
              uri: http://127.0.0.1:19004
              predicates:
                - Path=/product-center/**
              filters:
                - StripPrefix=1
                # 添加用户信息到请求头
                - AddRequestHeader=X-App, product-center
          globalcors:
            cors-configurations:
              '[/**]':
                allowedOriginPatterns: "*"
                allowedMethods:
                  - GET
                  - POST
                  - PUT
                  - DELETE
                  - OPTIONS
                allowedHeaders: "*"
                allowCredentials: true
                maxAge: 3600
  # Resource Server JWT 验证配置（通过 issuer 自动发现 JWK Set）
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://127.0.0.1:8889
          # JWK Set URI（可选，如果不设置会自动从 issuer-uri 发现）
          jwk-set-uri: http://127.0.0.1:8889/oauth2/jwks
  data:
    redis:
      database: 0
      host: 127.0.0.1
      port: 6379
      password: luc123456
      timeout: 5000
      client-type: lettuce
      lettuce:
        pool:
          min-idle: 8
          max-idle: 500
          max-active: 2000
          max-wait: 10000
          enabled: true


# 网关安全配置
gateway:
  security:
    white-paths:
      - "/health"
      - "/actuator/**"
      - "/gateway/health"
      - "/auth-server/login"
      - "/auth-server/auth/login"
      - "/auth-server/register"
      - "/auth-server/oauth2/**"
      - "/auth-server/.well-known/**"
      - "/auth-server/jwks"
      - "/swagger-ui/**"
      - "/v3/api-docs/**"
      - "/*/v3/api-docs/**"
      - "/webjars/**"
      - "/favicon.ico"
      - "/luc-system/**"
    jwt:
      issuer-uri: http://127.0.0.1:8889
      log-failures: true
      clock-skew-enabled: true
      clock-skew-seconds: 60

springdoc:
  swagger-ui:
    use-root-path: true
    oauth:
      client-id: openapi-client
      client-secret: secret
      scopes: read

# 日志配置
logging:
  level:
    com.lc.authorization.gateway: debug
    org.springframework.security: debug
    org.springframework.cloud.gateway: info
