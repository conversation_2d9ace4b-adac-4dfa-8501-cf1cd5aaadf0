<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.lc</groupId>
        <artifactId>luc-project</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>authorization-server</artifactId>
    <name>authorization-server</name>
    <description>authorization-server</description>
    <dependencies>

        <dependency>
            <groupId>com.lc</groupId>
            <artifactId>framework-security-authorization-server</artifactId>
        </dependency>

        <!-- LUC Framework 核心依赖 -->
        <dependency>
            <groupId>com.lc</groupId>
            <artifactId>framework-core</artifactId>
        </dependency>

        <!--web项目依赖-->
        <dependency>
            <groupId>com.lc</groupId>
            <artifactId>framework-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lc</groupId>
            <artifactId>system-api</artifactId>
        </dependency>

        <!--api文档-->
        <dependency>
            <groupId>com.lc</groupId>
            <artifactId>framework-apidoc</artifactId>
        </dependency>

        <!--监控-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
    </dependencies>

</project>
