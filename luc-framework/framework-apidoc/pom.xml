<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.lc</groupId>
        <artifactId>luc-framework</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>framework-apidoc</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lc</groupId>
            <artifactId>framework-core</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
</project>