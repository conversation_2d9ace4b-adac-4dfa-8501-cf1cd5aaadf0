<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.lc</groupId>
        <artifactId>luc-project</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>luc-framework</artifactId>
    <version>${revision}</version>

    <packaging>pom</packaging>

    <modules>
        <module>framework-apidoc</module>
        <module>framework-core</module>
        <module>framework-data-permission</module>
        <module>framework-datasource-starter</module>
        <module>framework-excel</module>
        <module>framework-redis-starter</module>
        <module>framework-storage</module>
        <module>framework-security</module>
        <module>framework-web</module>
    </modules>

    <dependencies>
<!--        <dependency>-->
<!--            <groupId>io.swagger.core.v3</groupId>-->
<!--            <artifactId>swagger-annotations-jakarta</artifactId>-->
<!--        </dependency>-->
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <!-- 所有脚手架均为无启动类的jar包， 需要在打包插件中配置跳过configuration-->
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.6.0</version>
                <configuration>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    <updatePomFile>true</updatePomFile>
                    <pomElements>
                        <parent>expand</parent>
                        <distributionManagement>remove</distributionManagement>
                        <repositories>remove</repositories>
                    </pomElements>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>