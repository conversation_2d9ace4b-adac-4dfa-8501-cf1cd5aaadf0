<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="${package}.mapper.UserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="${package}.domain.User">
        <id column="id" property="id" />
        <result column="username" property="username" />
        <result column="password" property="password" />
        <result column="email" property="email" />
        <result column="phone" property="phone" />
        <result column="real_name" property="realName" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, username, password, email, phone, real_name, status, 
        create_time, update_time, create_by, update_by, remark
    </sql>

    <!-- 自定义查询示例 -->
    <select id="selectUserWithRoles" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM sys_user 
        WHERE id = #{userId} 
        AND status = 1
    </select>

    <!-- 分页查询用户列表 -->
    <select id="selectUserPage" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM sys_user 
        <where>
            <if test="username != null and username != ''">
                AND username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="email != null and email != ''">
                AND email LIKE CONCAT('%', #{email}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
