<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.lc</groupId>
        <artifactId>framework-security</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>framework-security-authorization-server</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.lc</groupId>
            <artifactId>framework-security-core</artifactId>
        </dependency>

        <!-- Spring Authorization Server -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-authorization-server</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-client</artifactId>
        </dependency>

        <!--redis 依赖-->
        <dependency>
            <groupId>com.lc</groupId>
            <artifactId>framework-redis-starter</artifactId>
        </dependency>

        <!--jdbc依赖-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-crypto</artifactId>
            <version>${hutool.version}</version>
        </dependency>
    </dependencies>
</project>
