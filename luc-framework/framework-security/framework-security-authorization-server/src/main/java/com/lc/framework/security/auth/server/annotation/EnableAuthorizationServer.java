package com.lc.framework.security.auth.server.annotation;

import com.lc.framework.security.auth.server.SecurityAutoConfiguration;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * <pre>
 *     开启认证服务器
 * <pre/>
 * <AUTHOR> <PERSON>
 * @date : 2025/8/2 21:50
 * @version : 1.0
 * @see SecurityAutoConfiguration
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@Import(SecurityAutoConfiguration.class)
public @interface EnableAuthorizationServer {

}
