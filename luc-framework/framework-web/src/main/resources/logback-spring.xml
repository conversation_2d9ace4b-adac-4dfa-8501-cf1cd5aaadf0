<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
    <!-- 引入spirng boot默认的logback配置文件 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <springProperty scope="context" name="APP_NAME" source="spring.application.name"/>
    <property name="LOG_HOME" value="logs"/>
<!--    <springProperty scope="context" name="log.file.path" source="logging.file.path"/>-->
    <appender name="consoleApp" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </layout>
    </appender>

    <appender name="fileInfoApp" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder>
            <pattern>
                %date{yyyy-MM-dd HH:mm:ss.SSS} %-5level[%thread]%logger{56}.%method:%L -%msg%n
            </pattern>
            <charset>UTF-8</charset> <!-- 此处设置字符集 -->
        </encoder>
        <!-- 滚动策略 按日期，按大小记录-->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 路径 -->
            <fileNamePattern>${LOG_HOME}/${APP_NAME}_info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
<!--            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">-->
<!--                <maxFileSize>10MB</maxFileSize>-->
<!--            </timeBasedFileNamingAndTriggeringPolicy>-->
            <maxFileSize>10MB</maxFileSize>
            <!--日志文档保留天数-->
            <maxHistory>15</maxHistory>
        </rollingPolicy>
    </appender>

    <appender name="fileErrorApp" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <encoder>
            <pattern>
                %date{yyyy-MM-dd HH:mm:ss.SSS} %-5level[%thread]%logger{56}.%method:%L -%msg%n
            </pattern>
            <charset>UTF-8</charset> <!-- 此处设置字符集 -->
        </encoder>

        <!-- 设置滚动策略 按日期，按大小记录-->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 路径 -->
            <fileNamePattern>${LOG_HOME}/${APP_NAME}_error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>10MB</maxFileSize>
<!--            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">-->
<!--                <maxFileSize>10MB</maxFileSize>-->
<!--            </timeBasedFileNamingAndTriggeringPolicy>-->
            <!-- 控制保留的归档文件的最大数量，超出数量就删除旧文件，假设设置每个月滚动，
            且<maxHistory> 是1，则只保存最近1个月的文件，删除之前的旧文件 -->
            <maxHistory>15</maxHistory>
        </rollingPolicy>
    </appender>

    <appender name="plumelog" class="com.plumelog.logback.appender.RedisAppender">
        <appName>mybatisDemo</appName>
        <redisHost>127.0.0.1</redisHost>
        <redisAuth>luc123456</redisAuth>
        <redisPort>6379</redisPort>
        <runModel>2</runModel>
    </appender>

    <!-- root 一定要放在最后，因有加载顺序的问题 -->

    <springProfile name="dev">
        <root level="INFO">
            <appender-ref ref="consoleApp"/>
            <appender-ref ref="fileInfoApp"/>
            <appender-ref ref="fileErrorApp"/>
        </root>
    </springProfile>

    <springProfile name="prov">
        <root level="INFO">
            <appender-ref ref="consoleApp"/>
            <appender-ref ref="fileInfoApp"/>
            <appender-ref ref="fileErrorApp"/>
            <appender-ref ref="plumelog"/>
        </root>
    </springProfile>
</configuration>