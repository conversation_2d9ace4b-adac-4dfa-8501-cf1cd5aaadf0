{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@vben/tsconfig/web-app.json", "compilerOptions": {"baseUrl": ".", "paths": {"#/*": ["./src/*"], "@vben/common-ui": ["../../packages/effects/common-ui/src"], "@vben/common-ui/*": ["../../packages/effects/common-ui/src/*"], "@vben/plugins/vxe-table": ["../../packages/effects/plugins/src/vxe-table/index.ts"], "@vue/vite-config": ["../../node_modules/@vben/vite-config"]}, "types": ["vite/client", "@vben/types/global", "vue"]}, "references": [{"path": "./tsconfig.node.json"}], "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"]}