{"menu": {"title": "Menu List", "header": {"menuId": "Menu ID", "parentMenuId": "<PERSON><PERSON>", "title": "<PERSON>u Name", "authority": "Authority", "name": "Route Name", "path": "Route Path", "component": "Component", "redirect": "Redirect Path", "menuType": "Type", "status": "Status", "sortOrder": "Sort Order", "dtCreated": "Created Time", "meta": {"title": "Menu Title", "icon": "Menu Icon", "activeIcon": "Active Icon", "activePath": "Active Path", "authority": "Authority", "link": "Link Address"}, "operation": {"title": "Operation", "edit": "Edit", "delete": "Delete", "append": "Add Child"}}, "placeHolder": {"menuId": "Auto-generated from route name, rule: MenuPath -> menu-path", "parentMenuId": "Leave empty for top-level menu", "title": "Enter menu name", "authority": "Enter authority identifier", "name": "Enter route name (camelCase, must be unique)", "path": "Enter route path", "component": "Enter component path", "redirect": "Enter redirect path", "menuType": "Select menu type", "status": "Select status", "sortOrder": "Enter sort order", "meta": {"title": "Enter menu title", "icon": "Enter menu icon", "activeIcon": "Enter active icon", "activePath": "Enter active path", "authority": "Enter authority identifier", "link": "Enter link address"}}}}