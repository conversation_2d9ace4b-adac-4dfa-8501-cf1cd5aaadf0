// eslint-disable-next-line n/no-extraneous-import
import JSEncrypt from 'jsencrypt';

const jsencrypt = new JSEncrypt();
jsencrypt.setPublicKey(
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCWXsXu/lyPpXaPoRKbBrdJMnlboADDO/SosP7K5/oT5Ln1FKhOb719eHB4lufPYzjQhualbqgdf+A/fP1soa4w58jL9oeovWMTCoJNyQfZEB0d3+9KXk6NTiZMAGbm2sicCh9kviiEQ8bkZeTkhg5Es3SM0YKRkwHgoEwMysTaJwIDAQAB',
);

export function encrypt(plaintText: string) {
  return jsencrypt.encrypt(plaintText);
}
