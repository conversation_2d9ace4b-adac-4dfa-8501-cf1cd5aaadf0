// 对应env中的每个配置
export interface EnvConfig {
  VITE_BASE: string;
  VITE_GLOB_API_URL: string;
  VITE_OAUTH_BASE_URL: string;
  VITE_OAUTH_CLIENT_ID: string;
  VITE_OAUTH_CLIENT_SECRET: string;
  VITE_OAUTH_SCOPE: string;
  VITE_SESSION_KEY: string;
}

export interface OAuth2Config {
  server: string;
  clientId: string;
  clientSecret: string;
  scope: string;
}

export interface AppConfig {
  base: string;
  apiUrl: string;
  oauth2?: OAuth2Config;
  sessionKey: string;
}

export function useAppEnvConfig(env: Record<string, any>): AppConfig {
  // 生产环境下，直接使用 window._VBEN_ADMIN_PRO_APP_CONF_ 全局变量
  const config = env as EnvConfig;

  const {
    VITE_BASE,
    VITE_GLOB_API_URL,
    VITE_OAUTH_BASE_URL,
    VITE_OAUTH_CLIENT_ID,
    VITE_OAUTH_CLIENT_SECRET,
    VITE_OAUTH_SCOPE,
    VITE_SESSION_KEY,
  } = config;

  const applicationConfig: AppConfig = {
    base: VITE_BASE,
    apiUrl: VITE_GLOB_API_URL,
    sessionKey: VITE_SESSION_KEY,
  };
  if (VITE_OAUTH_BASE_URL && VITE_OAUTH_CLIENT_ID) {
    applicationConfig.oauth2 = {
      server: VITE_OAUTH_BASE_URL,
      clientId: VITE_OAUTH_CLIENT_ID,
      clientSecret: VITE_OAUTH_CLIENT_SECRET,
      scope: VITE_OAUTH_SCOPE,
    };
  }

  return applicationConfig;
}

export const DEFAULT_HOME_PATH = '/analytics';
