import { requestClient } from '#/api/request';

export namespace ProductApi {
  /**
   * 产品信息视图对象
   */
  export interface ProductInfoVO {
    /** 主键id */
    id?: number;
    /** 产品code */
    productCode?: string;
    /** 子产品code */
    subProductCode?: string;
    /** 计费项code */
    billingItemCode?: string;
    /** 子计费项code */
    subBillingItemCode?: string;
    /** 产品名称 */
    productName?: string;
    /** 子产品名称 */
    subProductName?: string;
    /** 计费项名称 */
    billingItemName?: string;
    /** 子计费项名称 */
    subBillingItemName?: string;
    /** 单位，个、次、GB等 */
    unit?: string;
    /** 价格 */
    price?: number;
    /** 计费规格 */
    chargeSize?: number;
    /** 生效状态（1生效 0失效） */
    status?: number;
    /** 状态描述 */
    statusDesc?: string;
    /** 创建者 */
    createdBy?: string;
    /** 创建时间 */
    dtCreated?: string;
    /** 更新者 */
    modifiedBy?: string;
    /** 更新时间 */
    dtModified?: string;
    /** 备注 */
    remark?: string;
  }

  /**
   * 产品信息数据传输对象
   */
  export interface ProductInfoDTO {
    /** 主键id */
    id?: number;
    /** 产品code */
    productCode?: string;
    /** 子产品code */
    subProductCode?: string;
    /** 计费项code */
    billingItemCode?: string;
    /** 子计费项code */
    subBillingItemCode?: string;
    /** 产品名称 */
    productName?: string;
    /** 子产品名称 */
    subProductName?: string;
    /** 计费项名称 */
    billingItemName?: string;
    /** 子计费项名称 */
    subBillingItemName?: string;
    /** 单位，个、次、GB等 */
    unit?: string;
    /** 价格 */
    price?: number;
    /** 计费规格 */
    chargeSize?: number;
    /** 生效状态（1生效 0失效） */
    status?: number;
    /** 创建者 */
    createdBy?: string;
    /** 创建时间 */
    dtCreated?: string;
    /** 更新者 */
    modifiedBy?: string;
    /** 更新时间 */
    dtModified?: string;
    /** 备注 */
    remark?: string;
  }

  /**
   * 分页响应对象
   */
  export interface PageResult<T> {
    /** 数据列表 */
    records: T[];
    /** 总记录数 */
    total: number;
    /** 当前页 */
    current: number;
    /** 页大小 */
    size: number;
    /** 总页数 */
    pages: number;
  }
}

// 产品中心服务前缀
const productCenterPrefix = '/product-center';

/**
 * 获取产品列表
 */
export async function getProductList(
  data?: ProductApi.ProductInfoDTO & { pageIndex?: number; pageSize?: number },
) {
  return requestClient.post<Array<ProductApi.ProductInfoVO>>(
    `${productCenterPrefix}/product/list`,
    data || {},
  );
}

/**
 * 获取产品详情
 */
export async function getProductDetail(id: number) {
  return requestClient.get<ProductApi.ProductInfoVO>(
    `${productCenterPrefix}/product/detail/${id}`,
  );
}

/**
 * 新增产品
 */
export async function createProduct(data: ProductApi.ProductInfoDTO) {
  return requestClient.post<ProductApi.ProductInfoVO>(
    `${productCenterPrefix}/product/create`,
    data,
  );
}

/**
 * 更新产品
 */
export async function updateProduct(
  id: number,
  data: ProductApi.ProductInfoDTO,
) {
  return requestClient.put<ProductApi.ProductInfoVO>(
    `${productCenterPrefix}/product/update/${id}`,
    data,
  );
}

/**
 * 删除产品
 */
export async function deleteProduct(id: number) {
  return requestClient.delete<boolean>(
    `${productCenterPrefix}/product/delete/${id}`,
  );
}

/**
 * 批量删除产品
 */
export async function batchDeleteProduct(ids: number[]) {
  return requestClient.post<boolean>(
    `${productCenterPrefix}/product/batch-delete`,
    { ids },
  );
}
