import { lucSystemPrefix, requestClient } from '../request';

export namespace RoleApi {
  export interface RoleInfoVO {
    /**
     * uid
     */
    id: number;
    /**
     * 角色id
     */
    roleId: string;
    /**
     * 名称
     */
    roleName: string;
    /**
     * 关联的菜单uid
     */
    menuUids: number[];
    /**
     * 描述
     */
    description?: string;
    /**
     * 状态（true启用，false禁用）
     */
    status: boolean;
    /**
     * 创建时间
     */
    dtCreated: Date;
  }
}

export async function list() {
  return requestClient.get<Array<RoleApi.RoleInfoVO>>(
    `${lucSystemPrefix}/role/list`,
  );
}
