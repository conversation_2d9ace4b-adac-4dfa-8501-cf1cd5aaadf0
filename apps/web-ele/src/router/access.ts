import type {
  ComponentRecordType,
  GenerateMenuAndRoutesOptions,
} from '@vben/types';

import { generateAccessible } from '@vben/access';
import { preferences } from '@vben/preferences';

import { ElMessage } from 'element-plus';

import { getAvailableMenuTree } from '#/api';
import { BasicLayout, IFrameView } from '#/layouts';
import { $t } from '#/locales';

const forbiddenComponent = () => import('#/views/_core/fallback/forbidden.vue');

async function generateAccess(options: GenerateMenuAndRoutesOptions) {
  try {
    const pageMap: ComponentRecordType = import.meta.glob('../views/**/*.vue');

    const layoutMap: ComponentRecordType = {
      BasicLayout,
      IFrameView,
    };

    return await generateAccessible(preferences.app.accessMode, {
      ...options,
      fetchMenuListAsync: async () => {
        try {
          ElMessage({
            duration: 1500,
            message: `${$t('common.loadingMenu')}...`,
          });
          return await getAvailableMenuTree();
        } catch (error) {
          console.error('获取菜单失败:', error);
          // 返回空菜单，避免阻塞路由生成
          return [];
        }
      },
      // 可以指定没有权限跳转403页面
      forbiddenComponent,
      // 如果 route.meta.menuVisibleWithForbidden = true
      layoutMap,
      pageMap,
    });
  } catch (error) {
    console.error('生成访问权限失败:', error);
    // 返回空的访问权限，避免阻塞路由
    return {
      accessibleMenus: [],
      accessibleRoutes: [],
    };
  }
}

export { generateAccess };
