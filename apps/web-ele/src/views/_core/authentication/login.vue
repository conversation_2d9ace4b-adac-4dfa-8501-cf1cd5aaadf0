<script lang="ts" setup>
import type { VbenFormSchema } from '@vben/common-ui';
import type { BasicOption } from '@vben/types';

import { computed, markRaw } from 'vue';

import { AuthenticationLogin, SliderCaptcha, z } from '@vben/common-ui';
import { MdiGithub, MdiWechat, SvgGiteeLogoIcon } from '@vben/icons';
import { $t } from '@vben/locales';

import { VbenIconButton } from '@vben-core/shadcn-ui';

import { useAuthStore } from '#/store';

defineOptions({ name: 'Login' });

const authStore = useAuthStore();

const MOCK_USER_OPTIONS: BasicOption[] = [
  {
    label: 'Super',
    value: 'vben',
  },
  {
    label: 'Admin',
    value: 'admin',
  },
  {
    label: 'User',
    value: 'jack',
  },
];

const formSchema = computed((): VbenFormSchema[] => {
  return [
    {
      component: 'VbenSelect',
      componentProps: {
        options: MOCK_USER_OPTIONS,
        placeholder: $t('authentication.selectAccount'),
      },
      fieldName: 'selectAccount',
      label: $t('authentication.selectAccount'),
      rules: z
        .string()
        .min(1, { message: $t('authentication.selectAccount') })
        .optional()
        .default('admin'),
    },
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: $t('authentication.usernameTip'),
      },
      dependencies: {
        trigger(values, form) {
          if (values.selectAccount) {
            const findUser = MOCK_USER_OPTIONS.find(
              (item) => item.value === values.selectAccount,
            );
            if (findUser) {
              form.setValues({
                password: 'admin123',
                username: findUser.value,
              });
            }
          }
        },
        triggerFields: ['selectAccount'],
      },
      fieldName: 'username',
      label: $t('authentication.username'),
      rules: z.string().min(1, { message: $t('authentication.usernameTip') }),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        placeholder: $t('authentication.password'),
      },
      fieldName: 'password',
      label: $t('authentication.password'),
      rules: z.string().min(1, { message: $t('authentication.passwordTip') }),
    },
    {
      component: markRaw(SliderCaptcha),
      fieldName: 'captcha',
      rules: z.boolean().refine((value) => value, {
        message: $t('authentication.verifyRequiredTip'),
      }),
    },
  ];
});
</script>

<template>
  <AuthenticationLogin
    :form-schema="formSchema"
    :loading="authStore.loginLoading"
    :show-code-login="true"
    code-login-path="/code-login"
    @submit="authStore.authLogin"
  >
    <template #third-party-login>
      <div class="w-full sm:mx-auto md:max-w-md">
        <div class="mt-4 flex items-center justify-between">
          <span
            class="border-input w-[35%] border-b dark:border-gray-600"
          ></span>
          <span class="text-muted-foreground text-center text-xs uppercase">
            {{ $t('authentication.thirdPartyLogin') }}
          </span>
          <span
            class="border-input w-[35%] border-b dark:border-gray-600"
          ></span>
        </div>

        <div class="mt-4 flex flex-wrap justify-center">
          <VbenIconButton class="mb-3" title="微信登录">
            <MdiWechat />
          </VbenIconButton>
          <VbenIconButton class="mb-3" title="GitHub登录">
            <MdiGithub />
          </VbenIconButton>
          <VbenIconButton
            class="mb-3"
            @click="authStore.oauth2Login('gitee')"
            title="Gitee登录"
          >
            <SvgGiteeLogoIcon />
          </VbenIconButton>
        </div>
      </div>
    </template>
  </AuthenticationLogin>
</template>
