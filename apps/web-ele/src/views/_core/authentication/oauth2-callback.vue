<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { useAppEnvConfig } from '#/utils/env-utils';

const route = useRoute();
const router = useRouter();

const loading = ref(true);
const message = ref('正在处理OAuth2登录...');
const LOGIN_PATH = ref('/auth/login');

onMounted(async () => {
  const { LOGIN_PATH: LOGIN_PATH_CONST } = await import('@vben/constants');
  const { DEFAULT_HOME_PATH } = await import('#/utils/env-utils');
  LOGIN_PATH.value = LOGIN_PATH_CONST;
  try {
    // 方式2: 标准OAuth2授权码流程
    const code = route.query.code as string;
    const error = route.query.error as string;

    if (error) {
      throw new Error(`OAuth2授权失败: ${error}`);
    }

    if (code) {
      message.value = '正在使用授权码获取访问令牌...';
      console.warn('OAuth2回调 - 授权码:', code);

      // 使用授权码获取token
      const { base, oauth2 } = useAppEnvConfig(import.meta.env);
      console.warn('OAuth2回调 - 配置信息:', { base, oauth2 });

      const redirect_uri = `${location.origin}${base}/oauth2/callback`;
      console.warn('OAuth2回调 - 重定向URI:', redirect_uri);
      try {
        const { getAccessTokenApi } = await import('#/api');
        const { useAccessStore, useUserStore } = await import('@vben/stores');
        // 1) 校验并读取 state（保留）
        const expectedState = sessionStorage.getItem('pkce_state');
        if (
          expectedState &&
          route.query.state &&
          expectedState !== route.query.state
        ) {
          throw new Error('state 校验失败');
        }
        // 2) 标准授权码换 token（客户端 Basic 认证）
        console.warn('OAuth2回调 - 开始获取访问令牌...');
        const tokenResult = await getAccessTokenApi({
          code,
          redirect_uri,
          client_id: oauth2?.clientId,
          client_secret: oauth2?.clientSecret,
          grant_type: 'authorization_code',
        });
        console.warn('OAuth2回调 - 获取到访问令牌:', tokenResult);
        // 清理 state
        sessionStorage.removeItem('pkce_state');
        const accessStore = useAccessStore();
        const userStore = useUserStore();
        accessStore.setAccessToken(tokenResult.access_token);
        // 3) 拉用户信息与权限码
        console.warn('OAuth2回调 - 开始获取用户信息...');
        const { getUserInfoApi } = await import('#/api');
        const userInfo = await getUserInfoApi();
        console.warn('OAuth2回调 - 获取到用户信息:', userInfo);
        userStore.setUserInfo(userInfo);
        accessStore.setAccessCodes(userInfo.permissions);
        // 4) 跳转
        const redirect =
          (route.query.redirect as string) ||
          userInfo.homePath ||
          DEFAULT_HOME_PATH;
        console.warn('OAuth2回调 - 准备跳转到:', redirect);
        await router.replace(redirect);
        message.value = '登录成功，正在跳转...';
      } catch (error) {
        console.error('OAuth2回调详细错误:', error);
        throw new Error(
          `OAuth2回调处理失败: ${error instanceof Error ? error.message : '未知错误'}`,
        );
      }
    } else {
      throw new Error('未找到授权码或token');
    }
  } catch (error) {
    console.error('OAuth2 回调处理失败:', error);
    message.value = `登录失败: ${error instanceof Error ? error.message : '未知错误'}`;

    // 3秒后跳转到登录页面
    setTimeout(() => {
      router.replace(`${LOGIN_PATH.value}?error=oauth2_callback_failed`);
    }, 3000);
  } finally {
    loading.value = false;
  }
});
</script>

<template>
  <div class="flex min-h-screen items-center justify-center bg-gray-50">
    <div
      class="mx-4 w-full max-w-md rounded-lg bg-white p-8 text-center shadow-lg"
    >
      <div v-if="loading" class="mb-6">
        <div
          class="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"
        ></div>
        <h2 class="mb-2 text-xl font-semibold text-gray-800">
          OAuth2 登录处理中
        </h2>
        <p class="text-gray-600">{{ message }}</p>
      </div>

      <div v-else class="mb-6">
        <div class="mx-auto mb-4 h-12 w-12">
          <svg
            v-if="message.includes('成功')"
            class="h-12 w-12 text-green-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M5 13l4 4L19 7"
            />
          </svg>
          <svg
            v-else
            class="h-12 w-12 text-red-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </div>
        <h2 class="mb-2 text-xl font-semibold text-gray-800">
          {{ message.includes('成功') ? '登录成功' : '登录失败' }}
        </h2>
        <p class="text-gray-600">{{ message }}</p>

        <div v-if="!message.includes('成功')" class="mt-4">
          <button
            @click="router.push(LOGIN_PATH)"
            class="rounded bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
          >
            返回登录页面
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
