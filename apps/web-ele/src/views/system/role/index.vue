<script lang="tsx" setup>
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { RoleA<PERSON> } from '#/api/luc_system/role';

import { Page, useVbenModal } from '@vben/common-ui';

import { ElButton } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { list } from '#/api/luc_system/role';

import RoleForm from './module/roleForm.vue';

defineOptions({
  name: 'RoleManage',
});

// 表格格式配置
const gridOptions: VxeTableGridOptions<RoleApi.RoleInfoVO> = {
  columns: [
    {
      field: 'roleId',
      title: '角色编码',
      width: 150,
    },
    {
      field: 'roleName',
      title: '角色名称',
      width: 200,
    },
    {
      field: 'description',
      title: '描述',
      minWidth: 200,
    },
    {
      field: 'status',
      title: '状态',
      width: 100,
      cellRender: {
        name: 'CellTag',
        props: ({ row }: { row: RoleApi.RoleInfoVO }) => ({
          text: row.status ? '启用' : '禁用',
          type: row.status ? 'success' : 'danger',
        }),
      },
    },
    {
      field: 'dtCreated',
      title: '创建时间',
      width: 180,
      formatter: ({ cellValue }) => {
        return cellValue ? new Date(cellValue).toLocaleString() : '';
      },
    },
    {
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 200,
      cellRender: {
        name: 'CellVbenButtonGroup',
        props: { gap: 10 },
        options: [
          {
            text: '查看',
            variant: 'info',
            handler: (row: RoleApi.RoleInfoVO) => onView(row),
          },
          {
            text: '编辑',
            variant: 'primary',
            handler: (row: RoleApi.RoleInfoVO) => onEdit(row),
          },
          {
            text: '删除',
            variant: 'destructive',
            handler: (row: RoleApi.RoleInfoVO) => onDelete(row),
          },
        ],
      },
    },
  ],
  rowConfig: { keyField: 'id' },
  proxyConfig: {
    ajax: {
      query: () => list(),
    },
    response: {
      list: '',
    },
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
};

// 表格点击事件配置
const gridEvents: VxeGridListeners<RoleApi.RoleInfoVO> = {
  cellClick: ({ row, column }: { column: any; row: RoleApi.RoleInfoVO }) => {
    if (column.field === 'roleName') {
      onView(row);
    }
  },
};

// 创建角色表格
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: RoleForm,
  destroyOnClose: true,
});

function onView(row: RoleApi.RoleInfoVO) {
  formModalApi
    .setData({
      actionType: 'view',
      row,
    })
    .open();
}

// 创建角色
function onCreate() {
  formModalApi.setData({ actionType: 'create' }).open();
}

// 编辑角色
function onEdit(row: RoleApi.RoleInfoVO) {
  formModalApi
    .setData({
      actionType: 'edit',
      row,
    })
    .open();
}

// 删除角色
function onDelete(row: RoleApi.RoleInfoVO) {
  formModalApi
    .setData({
      actionType: 'delete',
      row: { id: row.id, roleId: row.roleId },
    })
    .open();
}

// 刷新表格
function onRefresh() {
  gridApi.query();
}

// 监听表单提交成功事件
window.addEventListener('refreshRoleList', onRefresh);
</script>

<template>
  <Page
    description="角色管理页面，管理系统角色信息"
    title="角色管理"
    auto-content-height
  >
    <div class="mb-4">
      <ElButton type="primary" @click="onCreate">新增角色</ElButton>
      <ElButton @click="onRefresh" class="ml-2">刷新</ElButton>
    </div>
    <Grid />
    <FormModal />
  </Page>
</template>
