<script lang="tsx" setup>
import type { VbenFormSchema } from '#/adapter/form';
import type { RoleApi } from '#/api/luc_system/role';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElMessage } from 'element-plus';

import { useVbenForm } from '#/adapter/form';
import { getAvailableMenuTree } from '#/api/luc_system/menu';

// 定义操作类型
type ActionType = 'create' | 'delete' | 'edit' | 'view';

// 定义操作API映射
const actionApis = {
  create: (data: RoleApi.RoleInfoVO) => saveRole(data),
  delete: (data: RoleApi.RoleInfoVO) => deleteRole(data.roleId),
  edit: (data: RoleApi.RoleInfoVO) => saveRole(data),
};

const formData = ref<RoleApi.RoleInfoVO>();

// 表单配置
const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    // 设置标签宽度
    labelWidth: 100,
    // 添加冒号
    colon: true,
  },
  showDefaultActions: false,
  wrapperClass: 'grid grid-cols-1 lg:grid-cols-2',
  // 提交函数
  // handleSubmit: onSubmit,
  // 表单模式配置
  schema: [
    {
      label: '角色编码',
      fieldName: 'roleId',
      component: 'Input',
      componentProps: {
        placeholder: '请输入角色编码',
      },
      dependencies: {
        triggerFields: ['roleId'],
        disabled: () =>
          getActionType() === 'edit' || getActionType() === 'view',
      },
      rules: 'required',
    },
    {
      label: '角色名称',
      fieldName: 'roleName',
      component: 'Input',
      componentProps: {
        placeholder: '请输入角色名称',
      },
      dependencies: {
        triggerFields: ['roleName'],
        disabled: () => getActionType() === 'view',
      },
      rules: 'required',
    },
    {
      label: '描述',
      fieldName: 'description',
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入角色描述',
        rows: 3,
      },
      dependencies: {
        triggerFields: ['description'],
        disabled: () => getActionType() === 'view',
      },
    },
    {
      label: '状态',
      fieldName: 'status',
      component: 'Switch',
      componentProps: {
        checkedChildren: '启用',
        unCheckedChildren: '禁用',
      },
      dependencies: {
        triggerFields: ['status'],
        disabled: () => getActionType() === 'view',
      },
      defaultValue: true,
    },
    {
      label: '菜单权限',
      fieldName: 'menuUids',
      component: 'ApiTreeSelect',
      componentProps: {
        placeholder: '请选择菜单权限',
        api: async () => await getAvailableMenuTree(),
        labelField: 'meta.title',
        valueField: 'id',
        childrenField: 'children',
        defaultExpandAll: true,
        multiple: true,
        treeCheckable: true,
        showCheckedStrategy: 'SHOW_CHILD',
      },
      dependencies: {
        triggerFields: ['menuUids'],
        disabled: () => getActionType() === 'view',
      },
    },
  ] as VbenFormSchema[],
});

// 获取当前操作类型
function getActionType(): ActionType {
  return modalApi.getData<{ actionType: ActionType }>()?.actionType || 'view';
}

// 获取模态框标题
function getModalTitle(): string {
  const actionType = getActionType();
  const titles = {
    create: '新增角色',
    edit: '编辑角色',
    view: '查看角色',
    delete: '删除角色',
  };
  return titles[actionType];
}

// 模态框配置
const [Modal, modalApi] = useVbenModal({
  // 启用拖拽功能
  draggable: true,
  // 显示全屏按钮
  fullscreenButton: true,
  // 居中显示
  centered: true,
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      // 打开弹窗时，渲染表单数据
      const { actionType, row } = modalApi.getData<{
        actionType: ActionType;
        row?: RoleApi.RoleInfoVO;
      }>();

      if (row) {
        // 设置表单值
        formData.value = row;
        formApi.setValues(row);

        // 根据操作类型设置表单状态
        if (actionType === 'view') {
          // 查看模式：禁用所有字段
          formApi.setState({
            commonConfig: {
              disabled: true,
            },
          });
        } else {
          // 编辑/新增模式：启用所有字段
          formApi.setState({
            commonConfig: {
              disabled: false,
            },
          });
        }
      } else {
        // 重置表单
        formApi.resetForm();
      }
    }
  },
  async onConfirm() {
    const { actionType } = modalApi.getData<{
      actionType: ActionType;
    }>();
    const data = await formApi.getValues<RoleApi.RoleInfoVO>();
    // 表单验证
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    try {
      modalApi.lock();
      // 确保actionType是actionApis的有效键，并且formData存在
      if (actionType && actionType in actionApis && data) {
        await actionApis[actionType as keyof typeof actionApis](data);
        ElMessage.success('操作成功');
        modalApi.close();
      } else {
        ElMessage.error('输入有误');
      }
    } catch {
      ElMessage.error('操作失败');
    } finally {
      modalApi.unlock();
    }
  },
});

// 暴露模态框API
defineExpose({
  modalApi,
});

// TODO: 需要实现这些API函数
async function saveRole(_data: RoleApi.RoleInfoVO) {
  // 这里需要调用实际的保存API
}

async function deleteRole(_roleId: string) {
  // 这里需要调用实际的删除API
}
</script>

<template>
  <Modal :title="getModalTitle()" class="w-[600px]">
    <Form />
    <template #footer>
      <div class="flex justify-end gap-2">
        <button
          type="button"
          class="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
          @click="modalApi.close()"
        >
          取消
        </button>
        <button
          v-if="getActionType() !== 'view'"
          type="button"
          class="rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700"
          @click="modalApi.onConfirm()"
        >
          {{ getActionType() === 'delete' ? '确认删除' : '确定' }}
        </button>
      </div>
    </template>
  </Modal>
</template>
