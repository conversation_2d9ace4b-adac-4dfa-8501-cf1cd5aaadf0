<script lang="ts" setup>
import type { ProductApi } from '#/api';

import { ref } from 'vue';

import { useVbenForm, useVbenModal } from '@vben/common-ui';

import { ElMessage } from 'element-plus';

import { createProduct, deleteProduct, updateProduct } from '#/api';

// 定义操作类型
type ActionType = 'append' | 'create' | 'delete' | 'edit' | 'view';

// 定义操作API映射
const actionApis = {
  append: (data: ProductApi.ProductInfoDTO) => createProduct(data),
  create: (data: ProductApi.ProductInfoDTO) => createProduct(data),
  delete: (data: ProductApi.ProductInfoDTO) => deleteProduct(data.id!),
  edit: (data: ProductApi.ProductInfoDTO) => updateProduct(data.id!, data),
};

const formData = ref<ProductApi.ProductInfoDTO>();
// 表单配置
const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    labelWidth: 120,
    colon: true,
  },
  layout: 'horizontal',
  showDefaultActions: false,
  wrapperClass: 'grid grid-cols-1 lg:grid-cols-2',
  schema: [
    {
      label: '产品编码',
      fieldName: 'productCode',
      component: 'Input',
      componentProps: {
        placeholder: '请输入产品编码',
      },
      rules: 'required',
    },
    {
      label: '产品名称',
      fieldName: 'productName',
      component: 'Input',
      componentProps: {
        placeholder: '请输入产品名称',
      },
      rules: 'required',
    },
    {
      label: '子产品编码',
      fieldName: 'subProductCode',
      component: 'Input',
      componentProps: {
        placeholder: '请输入子产品编码',
      },
    },
    {
      label: '子产品名称',
      fieldName: 'subProductName',
      component: 'Input',
      componentProps: {
        placeholder: '请输入子产品名称',
      },
    },
    {
      label: '计费项编码',
      fieldName: 'billingItemCode',
      component: 'Input',
      componentProps: {
        placeholder: '请输入计费项编码',
      },
    },
    {
      label: '计费项名称',
      fieldName: 'billingItemName',
      component: 'Input',
      componentProps: {
        placeholder: '请输入计费项名称',
      },
    },
    {
      label: '子计费项编码',
      fieldName: 'subBillingItemCode',
      component: 'Input',
      componentProps: {
        placeholder: '请输入子计费项编码',
      },
    },
    {
      label: '子计费项名称',
      fieldName: 'subBillingItemName',
      component: 'Input',
      componentProps: {
        placeholder: '请输入子计费项名称',
      },
    },
    {
      label: '单位',
      fieldName: 'unit',
      component: 'Select',
      componentProps: {
        placeholder: '请选择单位',
        options: [
          { label: '个', value: '个' },
          { label: '次', value: '次' },
          { label: 'GB', value: 'GB' },
          { label: 'TB', value: 'TB' },
          { label: '小时', value: '小时' },
          { label: '天', value: '天' },
          { label: '月', value: '月' },
          { label: '年', value: '年' },
          { label: 'Mbps', value: 'Mbps' },
          { label: '万次', value: '万次' },
          { label: '万条', value: '万条' },
        ],
      },
    },
    {
      label: '价格',
      fieldName: 'price',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入价格',
        min: 0,
        precision: 2,
      },
    },
    {
      label: '计费规格',
      fieldName: 'chargeSize',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入计费规格',
        min: 0,
        precision: 2,
      },
    },
    {
      label: '状态',
      fieldName: 'status',
      component: 'RadioGroup',
      defaultValue: 1,
      componentProps: {
        options: [
          { label: '生效', value: 1 },
          { label: '失效', value: 0 },
        ],
      },
    },
    {
      label: '备注',
      fieldName: 'remark',
      component: 'Textarea',
      formItemClass: 'col-span-2',
      componentProps: {
        placeholder: '请输入备注信息',
        rows: 3,
      },
    },
  ],
});

// 获取当前操作类型
function getActionType(): ActionType {
  return modalApi.getData<{ actionType: ActionType }>()?.actionType || 'view';
}

function getModalTitle(): string {
  const actionType = getActionType();
  const titles = {
    create: '新增产品',
    edit: '编辑产品',
    view: '查看产品',
    append: '新增下级产品',
    delete: '删除产品',
  };
  return titles[actionType] || '产品管理';
}

// 模态框配置
const [Modal, modalApi] = useVbenModal({
  draggable: true,
  fullscreenButton: true,
  centered: true,
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const { actionType, row } = modalApi.getData<{
        actionType: ActionType;
        row?: ProductApi.ProductInfoVO;
      }>();

      if (row) {
        formData.value = row as ProductApi.ProductInfoDTO;
        formApi.setValues(row);

        if (actionType === 'view') {
          formApi.setState({
            commonConfig: {
              disabled: true,
            },
          });
        } else {
          formApi.setState({
            commonConfig: {
              disabled: false,
            },
          });
        }
      } else {
        formApi.resetForm();
      }
    }
  },
  onConfirm: async () => {
    try {
      await formApi.validate();
      const { actionType } = modalApi.getData<{
        actionType: ActionType;
        row?: ProductApi.ProductInfoVO;
      }>();

      const values = await formApi.getValues();
      const api = actionApis[actionType as keyof typeof actionApis];

      if (api) {
        await api(values as ProductApi.ProductInfoDTO);
        ElMessage.success('操作成功');
        modalApi.close();
        // 触发刷新事件
        window.dispatchEvent(new CustomEvent('refreshProductGrid'));
      }
    } catch (error) {
      console.error('操作失败:', error);
      ElMessage.error('操作失败');
    }
  },
});
</script>

<template>
  <Modal :title="getModalTitle()">
    <!-- 删除确认模式 -->
    <div v-if="getActionType() === 'delete'" class="p-8">
      <div class="flex flex-col items-center space-y-6">
        <div
          class="flex h-16 w-16 items-center justify-center rounded-full bg-red-100"
        >
          <svg
            class="h-8 w-8 text-red-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>
        <div class="space-y-2 text-center">
          <h3 class="text-xl font-semibold text-gray-900">确认删除产品</h3>
          <p class="text-gray-600">
            您即将删除产品
            <span class="font-medium text-red-600">{{
              modalApi.getData()?.row?.productName
            }}</span>
          </p>
          <p class="text-sm text-gray-500">此操作不可恢复，请谨慎操作</p>
        </div>
      </div>
    </div>

    <!-- 表单模式 -->
    <div v-else>
      <Form />
    </div>
  </Modal>
</template>
