<script lang="tsx" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { ProductApi } from '#/api';

import { onMounted } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { ElButton } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getProductList } from '#/api/product';

import CatalogForm from './module/catalogForm.vue';

defineOptions({
  name: 'ServiceCatalog',
});

// 打开新增模态框
function openCreateModal() {
  formModalApi
    .setData({
      actionType: 'create',
      row: undefined,
    })
    .open();
}

// 操作处理函数
function onView(row: ProductApi.ProductInfoVO) {
  formModalApi
    .setData({
      actionType: 'view',
      row,
    })
    .open();
}

function onEdit(row: ProductApi.ProductInfoVO) {
  formModalApi
    .setData({
      actionType: 'edit',
      row,
    })
    .open();
}

function onDelete(row: ProductApi.ProductInfoVO) {
  formModalApi
    .setData({
      actionType: 'delete',
      row,
    })
    .open();
}

// 表格格式配置
const gridOptions: VxeGridProps<ProductApi.ProductInfoVO> = {
  columns: [
    {
      field: 'productName',
      title: '产品名称',
      // treeNode: true,
      minWidth: 200,
      rowGroupNode: true,
      headerAlign: 'center',
      align: 'left',
    },
    {
      field: 'subProductName',
      title: '子产品名称',
      minWidth: 100,
    },
    {
      field: 'billingItemName',
      title: '收费项目名称',
      minWidth: 100,
    },
    {
      field: 'subBillingItemName',
      title: '子收费项目名称',
      minWidth: 100,
      // rowGroupNode: true,
    },
    {
      field: 'unit',
      title: '单位',
      width: 50,
    },
    {
      field: 'price',
      title: '价格',
      width: 50,
    },
    {
      field: 'chargeSize',
      title: '计费规格',
      width: 50,
    },
    {
      field: 'status',
      title: '状态',
      width: 80,
      cellRender: {
        name: 'CellTag',
        options: [
          {
            value: 1,
            color: 'success',
            label: $t('constants.status.enabled'),
          },
          {
            value: 0,
            type: 'error',
            label: $t('constants.status.disabled'),
          },
        ],
      },
    },
    {
      field: 'dtCreated',
      title: '创建时间',
      width: 180,
    },
    {
      title: '操作',
      width: 200,
      fixed: 'right',
      cellRender: {
        name: 'CellVbenButtonGroup',
        props: { gap: 10 },
        options: [
          {
            text: `${$t('form.menu.header.operation.edit')}`,
            variant: 'primary',
            handler: (row: ProductApi.ProductInfoVO) => onView(row),
          },
          {
            text: `${$t('form.menu.header.operation.edit')}`,
            variant: 'primary',
            handler: (row: ProductApi.ProductInfoVO) => onEdit(row),
          },
          {
            text: `${$t('form.menu.header.operation.delete')}`,
            variant: 'destructive',
            handler: (row: ProductApi.ProductInfoVO) => onDelete(row),
          },
        ],
      },
    },
  ],
  proxyConfig: {
    ajax: {
      query: async () => {
        const result = await getProductList();
        const products = result || [];
        // const treeData = buildProductTree(products);
        return {
          results: products,
        };
      },
    },
  },
  aggregateConfig: {
    groupFields: ['productCode', 'subProductCode', 'billingItemCode'],
    expandAll: true,
  },
  height: 'auto',
  keepSource: true,
};

// 产品表格
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

// 表单模态框
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: CatalogForm,
  destroyOnClose: true,
});

// 监听刷新事件
onMounted(() => {
  const handleRefresh = () => {
    gridApi.query();
  };

  window.addEventListener('refreshProductGrid', handleRefresh);

  return () => {
    window.removeEventListener('refreshProductGrid', handleRefresh);
  };
});
</script>

<template>
  <Page
    description="管理系统中的所有产品目录"
    title="产品目录管理"
    auto-content-height
  >
    <div class="mb-4">
      <ElButton type="primary" @click="openCreateModal">新增产品</ElButton>
    </div>
    <Grid />
    <FormModal />
  </Page>
</template>
