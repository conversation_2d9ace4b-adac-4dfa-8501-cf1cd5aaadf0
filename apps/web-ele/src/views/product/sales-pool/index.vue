<script lang="tsx" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import {
  ElButton,
  ElDescriptions,
  ElDescriptionsItem,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElMessage,
  ElOption,
  ElRadio,
  ElRadioGroup,
  ElSelect,
} from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

defineOptions({
  name: 'SalesPool',
});

interface ProductItem {
  id: string;
  productName: string;
  category: string;
  price: number;
  stock: number;
  salesStatus: string;
  description: string;
  createTime: Date;
}

// 产品分类选项
const categoryOptions = [
  { label: '云服务器', value: '云服务器' },
  { label: '云存储', value: '云存储' },
  { label: '云网络', value: '云网络' },
  { label: '云数据库', value: '云数据库' },
  { label: '云安全', value: '云安全' },
];

// 销售状态选项
const salesStatusOptions = [
  { label: '在售', value: '在售' },
  { label: '下架', value: '下架' },
  { label: '预售', value: '预售' },
];

function dataGenerator(index: number): ProductItem {
  const names = ['ECS实例', 'OSS存储', 'SLB负载均衡', 'RDS数据库', 'WAF防火墙'];
  const descriptions = [
    '高性能弹性云服务器实例，支持多种规格配置',
    '海量对象存储服务，安全可靠，成本低廉',
    '智能负载均衡服务，提升应用可用性',
    '高可用关系型数据库服务，支持多种引擎',
    'Web应用防火墙，保护网站安全',
  ];

  return {
    id: `product-${index + 1}`,
    productName: `${names[index % names.length]} ${index + 1}`,
    category: categoryOptions[index % categoryOptions.length]!.value,
    price: Number((Math.random() * 1000 + 100).toFixed(2)),
    stock: Math.floor(Math.random() * 1000) + 50,
    salesStatus: salesStatusOptions[index % salesStatusOptions.length]!.value,
    description: descriptions[index % descriptions.length]!,
    createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
  };
}

const MOCK_DATA = ref(
  Array.from({ length: 15 }).map<ProductItem>((_, index) =>
    dataGenerator(index),
  ),
);

// 创建新增模态框API
const [createModal, createModalApi] = useVbenModal({
  title: '添加产品',
  onCancel() {
    createModalApi.close();
  },
  async onConfirm() {
    try {
      const { formData } = createModalApi.getData<{
        formData: Partial<ProductItem>;
      }>();
      if (formData && formData.productName) {
        createModalApi.lock();
        await new Promise((resolve) => setTimeout(resolve, 1000));
        const newItem: ProductItem = {
          id: `product-${MOCK_DATA.value.length + 1}`,
          productName: formData.productName,
          category: formData.category || '云服务器',
          price: formData.price || 0,
          stock: formData.stock || 0,
          salesStatus: formData.salesStatus || '在售',
          description: formData.description || '',
          createTime: new Date(),
        };
        MOCK_DATA.value.push(newItem);
        ElMessage.success('添加成功');
        createModalApi.close();
      } else {
        ElMessage.warning('请填写产品名称');
      }
    } catch (error) {
      ElMessage.error('添加失败');
      console.error(error);
    } finally {
      createModalApi.unlock();
    }
  },
});

// 创建查看模态框API
const [viewModal, viewModalApi] = useVbenModal({
  title: '查看产品详情',
  showConfirmButton: false,
  onCancel() {
    viewModalApi.close();
  },
});

// 打开新增模态框
function openCreateModal() {
  createModalApi
    .setData({
      formData: {
        productName: '',
        category: '云服务器',
        price: 0,
        stock: 0,
        salesStatus: '在售',
        description: '',
      },
    })
    .open();
}

// 打开查看详情模态框
function openViewModal(row: ProductItem) {
  viewModalApi.setData({ row }).open();
}

// 表格格式配置
const gridOptions: VxeGridProps<ProductItem> = {
  columns: [
    { field: 'id', title: 'ID', type: 'seq', width: 50 },
    { field: 'productName', title: '产品名称', width: 200 },
    { field: 'category', title: '产品分类', width: 120 },
    { field: 'price', title: '价格(元)', width: 100 },
    { field: 'stock', title: '库存', width: 80 },
    {
      field: 'salesStatus',
      title: '销售状态',
      width: 100,
      cellRender: {
        name: 'CellTag',
        props: ({ row }: { row: ProductItem }) => {
          let color = 'error';
          if (row.salesStatus === '在售') {
            color = 'success';
          } else if (row.salesStatus === '预售') {
            color = 'warning';
          }
          return {
            color,
            text: row.salesStatus,
          };
        },
      },
    },
    { field: 'description', title: '产品描述', minWidth: 200 },
    { field: 'createTime', title: '创建时间', width: 180 },
    {
      field: 'action',
      fixed: 'right',
      title: '操作',
      width: 120,
      cellRender: {
        name: 'CellVbenButtonGroup',
        props: { gap: 10 },
        options: [
          {
            text: '查看',
            variant: 'primary',
            handler: (row: ProductItem) => {
              openViewModal(row);
            },
          },
        ],
      },
    },
  ],
  data: MOCK_DATA.value,
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
};

// 表格点击事件配置
const gridEvents: VxeGridListeners<ProductItem> = {
  cellClick: ({ row, column }) => {
    if (column.title === 'ID') {
      openViewModal(row);
    }
  },
};

const [Grid] = useVbenVxeGrid({ gridOptions, gridEvents });
</script>

<template>
  <Page description="管理系统中的产品售卖池" title="售卖池管理">
    <div class="mb-4">
      <ElButton type="primary" @click="openCreateModal">添加产品</ElButton>
    </div>
    <Grid />

    <!-- 查看模态框 -->
    <viewModal>
      <template #default>
        <ElDescriptions :column="1" border>
          <ElDescriptionsItem label="产品名称">
            {{ viewModalApi.getData()?.row?.productName }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="产品分类">
            {{ viewModalApi.getData()?.row?.category }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="价格">
            {{ viewModalApi.getData()?.row?.price }} 元
          </ElDescriptionsItem>
          <ElDescriptionsItem label="库存">
            {{ viewModalApi.getData()?.row?.stock }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="销售状态">
            {{ viewModalApi.getData()?.row?.salesStatus }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="产品描述">
            {{ viewModalApi.getData()?.row?.description }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="创建时间">
            {{ viewModalApi.getData()?.row?.createTime }}
          </ElDescriptionsItem>
        </ElDescriptions>
      </template>
    </viewModal>

    <!-- 创建模态框 -->
    <createModal>
      <template #default>
        <ElForm label-width="100px">
          <ElFormItem label="产品名称">
            <ElInput v-model="createModalApi.getData().formData.productName" />
          </ElFormItem>
          <ElFormItem label="产品分类">
            <ElSelect v-model="createModalApi.getData().formData.category">
              <ElOption
                v-for="option in categoryOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="价格(元)">
            <ElInputNumber
              v-model="createModalApi.getData().formData.price"
              :min="0"
              :precision="2"
            />
          </ElFormItem>
          <ElFormItem label="库存">
            <ElInputNumber
              v-model="createModalApi.getData().formData.stock"
              :min="0"
              :precision="0"
            />
          </ElFormItem>
          <ElFormItem label="销售状态">
            <ElRadioGroup
              v-model="createModalApi.getData().formData.salesStatus"
            >
              <ElRadio
                v-for="option in salesStatusOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </ElRadio>
            </ElRadioGroup>
          </ElFormItem>
          <ElFormItem label="产品描述">
            <ElInput
              v-model="createModalApi.getData().formData.description"
              type="textarea"
              :rows="3"
            />
          </ElFormItem>
        </ElForm>
      </template>
    </createModal>
  </Page>
</template>
