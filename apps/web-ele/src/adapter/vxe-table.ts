import { h } from 'vue';

import { VbenButton, VbenButtonGroup } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { setupVbenVxeTable, useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { get } from '@vben/utils';

import {
  ElButton,
  ElButtonGroup,
  ElImage,
  ElSwitch,
  ElTag,
} from 'element-plus';

import { useVbenForm } from './form';

setupVbenVxeTable({
  configVxeTable: (vxeUI) => {
    vxeUI.setConfig({
      grid: {
        align: 'center',
        border: false,
        columnConfig: {
          resizable: true,
        },
        minHeight: 180,
        formConfig: {
          // 全局禁用vxe-table的表单配置，使用formOptions
          enabled: false,
        },
        proxyConfig: {
          autoLoad: true,
          response: {
            result: 'results',
            list: 'results',
            total: 'total',
          },
          showActiveMsg: true,
          showResponseMsg: false,
        },
        round: true,
        showOverflow: true,
        size: 'small',
      },
    });

    // 表格配置项可以用 cellRender: { name: 'CellImage' },
    vxeUI.renderer.add('CellImage', {
      renderTableDefault(_renderOpts, params) {
        const { column, row } = params;
        const src = row[column.field];
        return h(ElImage, { src, previewSrcList: [src] });
      },
    });

    // 表格配置项可以用 cellRender: { name: 'CellButton' },
    vxeUI.renderer.add('CellButton', {
      renderTableDefault(_renderOpts) {
        const { props } = _renderOpts;
        return h(
          ElButton,
          { size: 'small', type: props?.type, round: props?.round },
          { default: () => props?.text },
        );
      },
    });

    // 这里可以自行扩展 vxe-table 的全局配置，比如自定义格式化
    // vxeUI.formats.add
    vxeUI.renderer.add('CellButtonGroup', {
      renderTableDefault(_renderOpts, params) {
        const { column, row } = params;
        const { options = [], props = {} } = _renderOpts;
        return h(
          ElButtonGroup,
          { type: props?.type, round: props?.round },
          {
            default: () =>
              options.map((opt) => {
                return h(
                  ElButton,
                  {
                    size: opt?.size || 'small',
                    type: opt.type || 'default',
                    onClick: () => {
                      if (typeof _renderOpts.events?.click === 'function') {
                        _renderOpts.events.click({ row, column, ...opt });
                      }
                    },
                    ...props,
                  },
                  { default: () => opt.text },
                );
              }),
          },
        );
      },
    });

    vxeUI.renderer.add('CellSwitch', {
      renderTableDefault(_renderOpts, params) {
        const { column, row } = params;
        return h(ElSwitch, { vModel: row[column.field] });
      },
    });

    vxeUI.renderer.add('CellTag', {
      renderTableDefault({ options }, { column, row }) {
        // 当前单元格的状态
        const value = get(row, column.field);
        const tagOptions = options ?? [
          { type: 'primary', label: $t('common.enabled'), value: 1 },
          { type: 'error', label: $t('common.disabled'), value: 0 },
        ];
        const tagItem = tagOptions.find((item) => item.value === value);
        return h(
          ElTag,
          {
            type: tagItem.type,
            label: tagItem.label,
            color: tagItem.color,
            // ...objectOmit(tagItem ?? {}, ['label']),
          },
          { default: () => tagItem?.label ?? value },
        );
      },
    });

    // 使用 VbenButtonGroup 的渲染器
    vxeUI.renderer.add('CellVbenButtonGroup', {
      renderTableDefault(_renderOpts, params) {
        const { row } = params;
        const { options = [], props = {} } = _renderOpts;
        const gap = props?.gap || 5;

        return h(
          VbenButtonGroup,
          { gap },
          {
            default: () =>
              options.map((opt) => {
                return h(
                  VbenButton,
                  {
                    size: opt?.size || 'small',
                    variant: opt.variant || 'default',
                    onClick: () => {
                      if (typeof opt.handler === 'function') {
                        opt.handler(row, params);
                      }
                    },
                    ...props,
                  },
                  { default: () => opt.text },
                );
              }),
          },
        );
      },
    });
  },
  useVbenForm,
});

export { useVbenVxeGrid };

export type * from '@vben/plugins/vxe-table';
