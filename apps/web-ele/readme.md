# 更新依赖包版本

```shell
# 检查当前分支
git branch --show-current

# 检查工作区状态
git status

# 备份 apps/web-ele 目录到临时位置
git branch -m deploy

# 添加上游仓库
git remote add upstream https://github.com/vbenjs/vue-vben-admin.git

# 获取所有标签和分支
git fetch upstream --tags

# 确认目标版本已获取
git tag | grep v5.5.9

# 使用 read-tree 将 v5.5.9 的内容覆盖到当前分支
# 这种方法不会引入历史记录
git read-tree -m -u v5.5.9

# 使用 read-tree 将 v5.5.9 的内容覆盖到当前分支
# 这种方法不会引入历史记录
# -m：允许合并操作
# -u：更新工作目录中的文件
git read-tree -m -u v5.5.9

# 删除被覆盖的 apps/web-ele 目录
rm -rf apps/web-ele

# 恢复原有的 apps/web-ele 目录
cp -r /tmp/web-ele-backup apps/web-ele

# 查看更改状态
git status

# 查看更改统计
git diff --stat --cached

# 添加所有更改到暂存区
git add .

# 提交更改
git commit -m "feat: 更新到 vue-vben-admin v5.5.9，保持 apps/web-ele 目录不变"

# 清理备份文件
rm -rf /tmp/web-ele-backup

# 移除临时远程仓库（可选）
git remote remove upstream

# 查看最近的提交记录
git log --oneline -5

# 检查项目结构
ls -la apps/
```
