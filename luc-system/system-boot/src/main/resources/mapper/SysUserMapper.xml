<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.system.mapper.SysUserMapper">

    <resultMap type="com.lc.system.domain.entity.SysUserDO" id="BASE_MAP">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="deptId" column="dept_id" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="nickName" column="nick_name" jdbcType="VARCHAR"/>
        <result property="userType" column="user_type" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="sex" column="sex" jdbcType="TINYINT"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="loginIp" column="login_ip" jdbcType="VARCHAR"/>
        <result property="loginDate" column="login_date" jdbcType="DATE"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="dtCreated" column="dt_created" jdbcType="DATE"/>
        <result property="modifiedBy" column="modified_by" jdbcType="VARCHAR"/>
        <result property="dtModified" column="dt_modified" jdbcType="DATE"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

</mapper>

