<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.lc</groupId>
        <artifactId>luc-system</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>system-boot</artifactId>

    <dependencies>
        <!--系统服务api-->
        <dependency>
            <groupId>com.lc</groupId>
            <artifactId>system-api</artifactId>
        </dependency>
        <!--rest规范及工具类-->
        <dependency>
            <groupId>com.lc</groupId>
            <artifactId>framework-core</artifactId>
        </dependency>
        <!--web依赖-->
        <dependency>
            <groupId>com.lc</groupId>
            <artifactId>framework-web</artifactId>
        </dependency>

        <!--data-permission-->
        <dependency>
            <groupId>com.lc</groupId>
            <artifactId>framework-data-permission</artifactId>
        </dependency>

        <!--datasource-->
<!--        <dependency>-->
<!--            <groupId>com.lc</groupId>-->
<!--            <artifactId>framework-datasource-starter</artifactId>-->
<!--            <version>1.0.0</version>-->
<!--        </dependency>-->

        <!--存储-->
        <dependency>
            <groupId>com.lc</groupId>
            <artifactId>framework-storage</artifactId>
        </dependency>

        <!--api文档-->
        <dependency>
            <groupId>com.lc</groupId>
            <artifactId>framework-apidoc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
    </dependencies>


</project>